# Résumé du Projet - Application de Dessin JavaFX

## 🎯 Objectif du Projet

Créer une application JavaFX de dessin de formes géométriques en utilisant des design patterns pour démontrer une architecture logicielle robuste et extensible.

## ✅ Fonctionnalités Réalisées

### Fonctionnalités Principales
- ✅ **Sélection de formes** : Rectangle, Cercle, Ligne
- ✅ **Dessin interactif** : Cliquer-glisser avec prévisualisation
- ✅ **Personnalisation** : Couleur et épaisseur du trait
- ✅ **Sauvegarde/Chargement** : Persistance en base SQLite
- ✅ **Journalisation** : 3 stratégies (Console, Fichier, BDD)
- ✅ **Interface complète** : Palette d'outils et zone de log

### Fonctionnalités Techniques
- ✅ **Architecture MVC** : Séparation claire des responsabilités
- ✅ **Base de données** : SQLite avec gestion automatique des tables
- ✅ **Gestion d'événements** : Souris et interface utilisateur
- ✅ **Configuration Eclipse** : Projet prêt à l'emploi

## 🏗️ Design Patterns Implémentés

| Pattern | Classe Principale | Usage |
|---------|------------------|-------|
| **Factory** | `ShapeFactory` | Création des formes géométriques |
| **Strategy** | `LoggingStrategy` | Stratégies de journalisation |
| **Observer** | `DrawingObserver` | Notification des changements |
| **Singleton** | `DatabaseManager` | Gestion unique de la BDD |
| **Template Method** | `Shape` | Structure commune du rendu |
| **MVC** | Toute l'architecture | Séparation Model-View-Controller |

## 📁 Structure du Projet

```
DrawingApp/
├── src/com/drawingapp/
│   ├── Main.java                    # Point d'entrée
│   ├── controller/                  # Contrôleurs MVC
│   ├── model/                       # Modèles de données
│   │   ├── shapes/                  # Formes géométriques
│   │   ├── drawing/                 # Gestion des dessins
│   │   └── database/                # Accès aux données
│   ├── view/                        # Interface utilisateur
│   └── patterns/                    # Design patterns
│       ├── factory/                 # Factory Pattern
│       ├── strategy/                # Strategy Pattern
│       └── observer/                # Observer Pattern
├── lib/                             # Dépendances (SQLite)
├── bin/                             # Classes compilées
└── Documentation/                   # Guides et README
```

## 🚀 Installation et Lancement

### Méthode 1 : Eclipse (Recommandée)
1. **Importer** le projet dans Eclipse
2. **Configurer** JavaFX (voir `GUIDE_ECLIPSE.md`)
3. **Lancer** `Main.java`

### Méthode 2 : Scripts automatiques
```bash
# Windows
download_dependencies.bat
run.bat

# Linux/Mac
./download_dependencies.sh
./run.sh
```

### Méthode 3 : Ligne de commande
```bash
javac -cp "lib/*" -d bin src/com/drawingapp/**/*.java
java -cp "bin;lib/*" --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml com.drawingapp.Main
```

## 🎨 Utilisation de l'Application

### Interface Utilisateur
1. **Panneau gauche** : Outils de dessin
   - Sélection de forme (Rectangle/Cercle/Ligne)
   - Choix de couleur
   - Réglage épaisseur du trait
   - Stratégie de journalisation
   - Sauvegarde/Chargement

2. **Panneau central** : Zone de dessin
   - Cliquer-glisser pour dessiner
   - Prévisualisation en temps réel

3. **Panneau droit** : Journal des actions
   - Affichage des logs selon la stratégie choisie

### Fonctionnalités Avancées
- **Sauvegarde** : Nommer et sauvegarder le dessin
- **Chargement** : Sélectionner et charger un dessin existant
- **Journalisation** : Changer la stratégie de log dynamiquement
- **Effacement** : Vider la zone de dessin

## 🗄️ Base de Données

### Tables créées automatiquement :
- **drawings** : Métadonnées des dessins
- **shapes** : Formes géométriques avec propriétés
- **logs** : Journalisation (si stratégie BDD activée)

### Fichiers générés :
- `drawing_app.db` : Base SQLite
- `drawing_app.log` : Fichier de log (si stratégie fichier)

## 🔧 Configuration Technique

### Prérequis
- **Java 11+** : Version minimale requise
- **JavaFX 17+** : Interface graphique
- **SQLite JDBC** : Base de données (inclus)
- **Eclipse IDE** : Environnement de développement

### Dépendances
- `sqlite-jdbc-********.jar` : Driver SQLite
- JavaFX SDK : Interface graphique (externe)

## 📚 Documentation

| Fichier | Description |
|---------|-------------|
| `README.md` | Guide principal d'installation |
| `GUIDE_ECLIPSE.md` | Configuration détaillée Eclipse |
| `DESIGN_PATTERNS.md` | Explication des patterns utilisés |
| `run_config.txt` | Arguments de lancement |

## 🎓 Aspects Pédagogiques

### Concepts Démontrés
- **Architecture logicielle** : Séparation des responsabilités
- **Design patterns** : Application pratique de 6 patterns
- **Programmation événementielle** : Gestion des interactions utilisateur
- **Persistance de données** : Base de données relationnelle
- **Interface graphique** : JavaFX moderne

### Extensibilité
- **Nouvelles formes** : Ajouter facilement via Factory
- **Nouvelles stratégies** : Implémenter LoggingStrategy
- **Nouveaux observateurs** : S'enregistrer auprès du Drawing
- **Nouvelles fonctionnalités** : Architecture modulaire

## ✨ Points Forts

1. **Architecture robuste** : Design patterns bien intégrés
2. **Code maintenable** : Structure claire et documentée
3. **Interface intuitive** : Utilisation simple et efficace
4. **Extensibilité** : Facile d'ajouter de nouvelles fonctionnalités
5. **Persistance complète** : Sauvegarde/chargement fonctionnels
6. **Journalisation flexible** : 3 stratégies interchangeables
7. **Configuration Eclipse** : Projet prêt à l'emploi

## 🎯 Objectifs Atteints

- ✅ Application JavaFX fonctionnelle
- ✅ 6 design patterns implémentés et intégrés
- ✅ Interface utilisateur complète
- ✅ Persistance en base de données
- ✅ Journalisation multi-stratégies
- ✅ Architecture extensible et maintenable
- ✅ Documentation complète
- ✅ Configuration Eclipse simplifiée

## 🚀 Prêt à l'Utilisation

Le projet est entièrement fonctionnel et prêt à être utilisé dans Eclipse. Suivez le guide d'installation et lancez l'application pour découvrir toutes ses fonctionnalités !
