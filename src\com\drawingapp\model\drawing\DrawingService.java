package com.drawingapp.model.drawing;

import com.drawingapp.model.database.DatabaseManager;
import com.drawingapp.model.shapes.Shape;
import com.drawingapp.patterns.factory.ShapeFactory;
import javafx.scene.paint.Color;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Service pour la gestion de la persistance des dessins
 */
public class DrawingService {
    
    private DatabaseManager dbManager;
    
    public DrawingService() {
        this.dbManager = DatabaseManager.getInstance();
    }
    
    /**
     * Sauvegarde un dessin en base de données
     * @param drawing Dessin à sauvegarder
     * @throws SQLException En cas d'erreur de base de données
     */
    public void saveDrawing(Drawing drawing) throws SQLException {
        Connection conn = dbManager.getConnection();
        
        // Commencer une transaction
        conn.setAutoCommit(false);
        
        try {
            // Supprimer le dessin existant s'il existe
            deleteDrawingByName(drawing.getName());
            
            // Insérer le nouveau dessin
            String insertDrawingSql = "INSERT INTO drawings (name) VALUES (?)";
            int drawingId;
            
            try (PreparedStatement stmt = conn.prepareStatement(insertDrawingSql, Statement.RETURN_GENERATED_KEYS)) {
                stmt.setString(1, drawing.getName());
                stmt.executeUpdate();
                
                try (ResultSet rs = stmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        drawingId = rs.getInt(1);
                    } else {
                        throw new SQLException("Échec de la création du dessin, aucun ID généré.");
                    }
                }
            }
            
            // Insérer les formes
            String insertShapeSql = "INSERT INTO shapes (drawing_id, shape_type, start_x, start_y, end_x, end_y, color, stroke_width) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            try (PreparedStatement stmt = conn.prepareStatement(insertShapeSql)) {
                for (Shape shape : drawing.getShapes()) {
                    stmt.setInt(1, drawingId);
                    stmt.setString(2, shape.getShapeType());
                    stmt.setDouble(3, shape.getStartX());
                    stmt.setDouble(4, shape.getStartY());
                    stmt.setDouble(5, shape.getEndX());
                    stmt.setDouble(6, shape.getEndY());
                    stmt.setString(7, colorToString(shape.getColor()));
                    stmt.setDouble(8, shape.getStrokeWidth());
                    stmt.addBatch();
                }
                stmt.executeBatch();
            }
            
            // Valider la transaction
            conn.commit();
            
        } catch (SQLException e) {
            // Annuler la transaction en cas d'erreur
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }
    
    /**
     * Charge un dessin depuis la base de données
     * @param drawingName Nom du dessin à charger
     * @return Dessin chargé ou null si non trouvé
     * @throws SQLException En cas d'erreur de base de données
     */
    public Drawing loadDrawing(String drawingName) throws SQLException {
        Connection conn = dbManager.getConnection();
        
        // Récupérer l'ID du dessin
        String getDrawingIdSql = "SELECT id FROM drawings WHERE name = ?";
        int drawingId;
        
        try (PreparedStatement stmt = conn.prepareStatement(getDrawingIdSql)) {
            stmt.setString(1, drawingName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    drawingId = rs.getInt("id");
                } else {
                    return null; // Dessin non trouvé
                }
            }
        }
        
        // Récupérer les formes
        String getShapesSql = "SELECT shape_type, start_x, start_y, end_x, end_y, color, stroke_width " +
                "FROM shapes WHERE drawing_id = ?";

        Drawing drawing = new Drawing(drawingName);
        
        try (PreparedStatement stmt = conn.prepareStatement(getShapesSql)) {
            stmt.setInt(1, drawingId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Shape shape = ShapeFactory.createShape(
                        rs.getString("shape_type"),
                        rs.getDouble("start_x"),
                        rs.getDouble("start_y"),
                        rs.getDouble("end_x"),
                        rs.getDouble("end_y")
                    );
                    
                    shape.setColor(stringToColor(rs.getString("color")));
                    shape.setStrokeWidth(rs.getDouble("stroke_width"));
                    
                    drawing.addShape(shape);
                }
            }
        }
        
        return drawing;
    }
    
    /**
     * Récupère la liste des noms de dessins sauvegardés
     * @return Liste des noms de dessins
     * @throws SQLException En cas d'erreur de base de données
     */
    public List<String> getDrawingNames() throws SQLException {
        List<String> names = new ArrayList<>();
        Connection conn = dbManager.getConnection();
        
        String sql = "SELECT name FROM drawings ORDER BY created_date DESC";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                names.add(rs.getString("name"));
            }
        }
        
        return names;
    }
    
    /**
     * Supprime un dessin par son nom
     * @param drawingName Nom du dessin à supprimer
     * @throws SQLException En cas d'erreur de base de données
     */
    private void deleteDrawingByName(String drawingName) throws SQLException {
        Connection conn = dbManager.getConnection();
        String sql = "DELETE FROM drawings WHERE name = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, drawingName);
            stmt.executeUpdate();
        }
    }
    
    // Méthodes utilitaires pour la conversion des couleurs
    private String colorToString(Color color) {
        return String.format("#%02X%02X%02X", 
            (int)(color.getRed() * 255),
            (int)(color.getGreen() * 255),
            (int)(color.getBlue() * 255));
    }
    
    private Color stringToColor(String colorString) {
        try {
            return Color.web(colorString);
        } catch (Exception e) {
            return Color.BLACK; // Couleur par défaut
        }
    }
}
