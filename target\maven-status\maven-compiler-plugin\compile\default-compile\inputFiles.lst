C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\drawing\DrawingService.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\strategy\DatabaseLoggingStrategy.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\shapes\Rectangle.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\strategy\LoggingStrategy.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\drawing\Drawing.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\shapes\Line.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\factory\ShapeFactory.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\view\DrawingView.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\observer\DrawingObserver.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\strategy\ConsoleLoggingStrategy.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\shapes\Circle.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\shapes\Shape.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\controller\DrawingController.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\patterns\strategy\FileLoggingStrategy.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\Main.java
C:\Users\<USER>\Documents\test_projetfx\src\main\java\com\drawingapp\model\database\DatabaseManager.java
