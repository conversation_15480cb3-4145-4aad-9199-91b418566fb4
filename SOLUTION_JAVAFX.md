# Solution : Erreur "NoClassDefFoundError: Stage"

## Problème
L'erreur `java.lang.NoClassDefFoundError: Stage` indique que JavaFX n'est pas correctement configuré dans Eclipse.

## Solution Complète

### 1. Télécharger JavaFX SDK

1. **Aller sur** https://openjfx.io/
2. **Télécharger** JavaFX SDK 17+ pour votre OS
3. **Extraire** dans un dossier permanent :
   - Windows : `C:\javafx-sdk-17.0.2`
   - Linux : `/opt/javafx-sdk-17.0.2`
   - Mac : `/Library/Java/javafx-sdk-17.0.2`

### 2. Configurer Eclipse

#### A. Créer la User Library JavaFX

1. **Eclipse > Window > Preferences**
2. **Java > Build Path > User Libraries**
3. **New...** → Nom : `JavaFX`
4. **Sélectionner JavaFX > Add External JARs...**
5. **Naviguer** vers `javafx-sdk-17.0.2/lib/`
6. **Sélectionner TOUS les JARs** :
   ```
   javafx.base.jar
   javafx.controls.jar
   javafx.fxml.jar
   javafx.graphics.jar
   javafx.media.jar
   javafx.swing.jar
   javafx.web.jar
   ```
7. **OK > Apply and Close**

#### B. Ajouter JavaFX au projet

1. **Clic droit sur projet > Properties**
2. **Java Build Path > Libraries**
3. **Add Library... > User Library > Next**
4. **Cocher JavaFX > Finish > Apply and Close**

### 3. Configurer le lancement

#### A. Créer une Run Configuration

1. **Clic droit sur Main.java > Run As > Run Configurations...**
2. **Java Application > New Configuration**
3. **Name** : `DrawingApp`
4. **Project** : `DrawingApp`
5. **Main class** : `com.drawingapp.Main`

#### B. Arguments VM

Dans **Arguments > VM arguments** :

**Windows :**
```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml
```

**Linux/Mac :**
```
--module-path "/opt/javafx-sdk-17.0.2/lib" --add-modules javafx.controls,javafx.fxml
```

#### C. Lancer

1. **Apply > Run**

### 4. Alternative : Modifier le classpath

Si la méthode ci-dessus ne fonctionne pas, modifier `.classpath` :

```xml
<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-11">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="lib" path="lib/sqlite-jdbc-********.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.base.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.controls.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.fxml.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.graphics.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.media.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.swing.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.web.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
```

### 5. Vérification

Pour tester si JavaFX est bien configuré :

1. **Créer un test simple** :

```java
package com.drawingapp;

import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.stage.Stage;

public class TestJavaFX extends Application {
    @Override
    public void start(Stage stage) {
        Label label = new Label("JavaFX fonctionne !");
        Scene scene = new Scene(label, 300, 200);
        stage.setTitle("Test JavaFX");
        stage.setScene(scene);
        stage.show();
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
```

2. **Lancer TestJavaFX** avec les mêmes arguments VM

### 6. Dépannage

#### Erreur "Module not found"
- Vérifier le chemin dans les arguments VM
- Vérifier que JavaFX SDK est bien téléchargé

#### Erreur "Access denied"
- Vérifier les permissions du dossier JavaFX
- Essayer de déplacer JavaFX dans un dossier sans espaces

#### Erreur de version Java
- Vérifier que Java 11+ est utilisé
- JavaFX 17+ nécessite Java 11+

### 7. Alternative : Java avec JavaFX intégré

Si vous avez des difficultés, utilisez :
- **Liberica JDK Full** (inclut JavaFX)
- **Azul Zulu FX** (inclut JavaFX)

Téléchargeable sur :
- https://bell-sw.com/pages/downloads/
- https://www.azul.com/downloads/

## Résultat Attendu

Après configuration correcte :
- L'application se lance sans erreur
- Une fenêtre JavaFX s'ouvre
- L'interface de dessin est visible et fonctionnelle
