package com.drawingapp.model.shapes;

import javafx.scene.canvas.GraphicsContext;

/**
 * Implémentation concrète d'un cercle
 */
public class Circle extends Shape {
    
    public Circle(double startX, double startY, double endX, double endY) {
        super(startX, startY, endX, endY);
        this.shapeType = "Circle";
    }
    
    @Override
    protected void drawShape(GraphicsContext gc) {
        double centerX = startX;
        double centerY = startY;
        double radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        
        gc.strokeOval(centerX - radius, centerY - radius, radius * 2, radius * 2);
    }
}
