package com.drawingapp.model.drawing;

import com.drawingapp.model.shapes.Shape;
import com.drawingapp.patterns.observer.DrawingObserver;
import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON>d<PERSON><PERSON> représentant un dessin
 * Implémente le pattern Observer pour notifier les changements
 */
public class Drawing {
    
    private String name;
    private List<Shape> shapes;
    private List<DrawingObserver> observers;
    
    public Drawing() {
        this.shapes = new ArrayList<>();
        this.observers = new ArrayList<>();
    }
    
    public Drawing(String name) {
        this();
        this.name = name;
    }
    
    /**
     * Ajoute une forme au dessin
     * @param shape Forme à ajouter
     */
    public void addShape(Shape shape) {
        shapes.add(shape);
        notifyShapeAdded(shape);
    }
    
    /**
     * Efface toutes les formes du dessin
     */
    public void clear() {
        shapes.clear();
        notifyDrawingCleared();
    }
    
    /**
     * Ajoute un observateur
     * @param observer Observateur à ajouter
     */
    public void addObserver(DrawingObserver observer) {
        observers.add(observer);
    }
    
    /**
     * Supprime un observateur
     * @param observer Observateur à supprimer
     */
    public void removeObserver(DrawingObserver observer) {
        observers.remove(observer);
    }
    
    // Méthodes de notification
    private void notifyShapeAdded(Shape shape) {
        for (DrawingObserver observer : observers) {
            observer.onShapeAdded(shape);
        }
    }
    
    private void notifyDrawingCleared() {
        for (DrawingObserver observer : observers) {
            observer.onDrawingCleared();
        }
    }
    
    public void notifyDrawingLoaded(String drawingName) {
        for (DrawingObserver observer : observers) {
            observer.onDrawingLoaded(drawingName);
        }
    }
    
    public void notifyDrawingSaved(String drawingName) {
        for (DrawingObserver observer : observers) {
            observer.onDrawingSaved(drawingName);
        }
    }
    
    // Getters et Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public List<Shape> getShapes() { return new ArrayList<>(shapes); }
    public void setShapes(List<Shape> shapes) { 
        this.shapes = new ArrayList<>(shapes); 
    }
    
    public int getShapeCount() { return shapes.size(); }
}
