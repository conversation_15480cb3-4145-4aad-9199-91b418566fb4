package com.drawingapp.model.database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * Gestionnaire de base de données utilisant le pattern Singleton
 * Gère la connexion SQLite pour la persistance des dessins
 */
public class DatabaseManager {
    
    private static DatabaseManager instance;
    private Connection connection;
    private static final String DB_URL = "**************************";
    
    private DatabaseManager() {
        // Constructeur privé pour le pattern Singleton
    }
    
    /**
     * Retourne l'instance unique du gestionnaire de base de données
     * @return Instance du DatabaseManager
     */
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    /**
     * Initialise la connexion à la base de données et crée les tables
     */
    public void initializeDatabase() throws SQLException {
        connection = DriverManager.getConnection(DB_URL);
        createTables();
    }
    
    /**
     * Crée les tables nécessaires si elles n'existent pas
     */
    private void createTables() throws SQLException {
        // Table pour les dessins
        String createDrawingsTable = "CREATE TABLE IF NOT EXISTS drawings (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "name TEXT UNIQUE NOT NULL, " +
                "created_date DATETIME DEFAULT CURRENT_TIMESTAMP" +
                ")";

        // Table pour les formes
        String createShapesTable = "CREATE TABLE IF NOT EXISTS shapes (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "drawing_id INTEGER NOT NULL, " +
                "shape_type TEXT NOT NULL, " +
                "start_x REAL NOT NULL, " +
                "start_y REAL NOT NULL, " +
                "end_x REAL NOT NULL, " +
                "end_y REAL NOT NULL, " +
                "color TEXT, " +
                "stroke_width REAL, " +
                "FOREIGN KEY (drawing_id) REFERENCES drawings (id) ON DELETE CASCADE" +
                ")";

        try (PreparedStatement stmt1 = connection.prepareStatement(createDrawingsTable);
             PreparedStatement stmt2 = connection.prepareStatement(createShapesTable)) {

            stmt1.executeUpdate();
            stmt2.executeUpdate();
        }
    }
    
    /**
     * Retourne la connexion à la base de données
     * @return Connexion active
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * Ferme la connexion à la base de données
     */
    public void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                System.err.println("Erreur lors de la fermeture de la base de données: " + e.getMessage());
            }
        }
    }
}
