package com.drawingapp.patterns.strategy;

import com.drawingapp.model.database.DatabaseManager;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * Stratégie de journalisation dans une base de données
 */
public class DatabaseLoggingStrategy implements LoggingStrategy {
    
    private DatabaseManager dbManager;
    
    @Override
    public void log(String message) {
        try {
            Connection conn = dbManager.getConnection();
            String sql = "INSERT INTO logs (timestamp, message) VALUES (?, ?)";
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setObject(1, LocalDateTime.now());
                stmt.setString(2, message);
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            System.err.println("Erreur lors de l'enregistrement du log en base: " + e.getMessage());
        }
    }
    
    @Override
    public void initialize() {
        dbManager = DatabaseManager.getInstance();
        try {
            createLogTableIfNotExists();
            log("Initialisation de la journalisation base de données");
        } catch (SQLException e) {
            System.err.println("Erreur lors de l'initialisation de la table de logs: " + e.getMessage());
        }
    }
    
    private void createLogTableIfNotExists() throws SQLException {
        Connection conn = dbManager.getConnection();
        String sql = "CREATE TABLE IF NOT EXISTS logs (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    "timestamp DATETIME NOT NULL, " +
                    "message TEXT NOT NULL)";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.executeUpdate();
        }
    }
    
    @Override
    public void close() {
        if (dbManager != null) {
            log("Fermeture de la journalisation base de données");
        }
    }
    
    @Override
    public String getStrategyName() {
        return "Base de données";
    }
}
