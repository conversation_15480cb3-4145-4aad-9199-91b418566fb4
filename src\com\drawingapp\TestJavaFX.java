package com.drawingapp;

import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Test simple pour vérifier que JavaFX fonctionne
 */
public class TestJavaFX extends Application {
    
    @Override
    public void start(Stage primaryStage) {
        Label titleLabel = new Label("✅ JavaFX fonctionne correctement !");
        Label infoLabel = new Label("Si vous voyez cette fenêtre, JavaFX est bien configuré.");
        Label nextLabel = new Label("Vous pouvez maintenant lancer Main.java");
        
        titleLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: green;");
        infoLabel.setStyle("-fx-font-size: 12px;");
        nextLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: blue;");
        
        VBox root = new VBox(20);
        root.setStyle("-fx-padding: 30; -fx-alignment: center;");
        root.getChildren().addAll(titleLabel, infoLabel, nextLabel);
        
        Scene scene = new Scene(root, 400, 200);
        primaryStage.setTitle("Test JavaFX - Application de Dessin");
        primaryStage.setScene(scene);
        primaryStage.show();
        
        System.out.println("✅ JavaFX Test réussi !");
        System.out.println("Vous pouvez fermer cette fenêtre et lancer Main.java");
    }
    
    public static void main(String[] args) {
        System.out.println("🔧 Test de configuration JavaFX...");
        try {
            launch(args);
        } catch (Exception e) {
            System.err.println("❌ Erreur JavaFX : " + e.getMessage());
            System.err.println("Veuillez suivre le guide SOLUTION_JAVAFX.md");
        }
    }
}
