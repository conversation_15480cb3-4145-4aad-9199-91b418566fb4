package com.drawingapp.patterns.strategy;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Stratégie de journalisation dans un fichier texte
 */
public class FileLoggingStrategy implements LoggingStrategy {
    
    private static final String LOG_FILE = "drawing_app.log";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private PrintWriter writer;
    
    @Override
    public void log(String message) {
        if (writer != null) {
            String timestamp = LocalDateTime.now().format(FORMATTER);
            writer.println("[" + timestamp + "] " + message);
            writer.flush(); // Force l'écriture immédiate
        }
    }
    
    @Override
    public void initialize() {
        try {
            writer = new PrintWriter(new FileWriter(LOG_FILE, true)); // Mode append
            log("Initialisation de la journalisation fichier");
        } catch (IOException e) {
            System.err.println("Erreur lors de l'initialisation du fichier de log: " + e.getMessage());
        }
    }
    
    @Override
    public void close() {
        if (writer != null) {
            log("Fermeture de la journalisation fichier");
            writer.close();
        }
    }
    
    @Override
    public String getStrategyName() {
        return "Fichier";
    }
}
