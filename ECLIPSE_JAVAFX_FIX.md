# Solution : Module javafx.base not found

## Problème
L'erreur `Module javafx.base not found` indique que JavaFX SDK n'est pas correctement installé ou configuré.

## Solution Complète

### 1. Vérifier JavaFX SDK

Exécuter le script de vérification :
```bash
check_javafx.bat
```

### 2. Télécharger JavaFX SDK (si nécessaire)

1. **Aller sur** https://openjfx.io/
2. **Télécharger** JavaFX SDK (pas Runtime !)
3. **Choisir** la version 17+ compatible avec votre Java
4. **Extraire** dans `C:\javafx-sdk-17.0.2`

### 3. Configuration Eclipse - Méthode 1 (User Library)

#### A. Créer la User Library
1. **Eclipse > Window > Preferences**
2. **Java > Build Path > User Libraries**
3. **New...** → Nom : `JavaFX`
4. **Add External JARs...** → Sélectionner TOUS les JARs de `javafx-sdk-17.0.2/lib/`

#### B. Ajouter au projet
1. **Clic droit projet > Properties**
2. **Java Build Path > Libraries**
3. **Add Library... > User Library > JavaFX**

#### C. Arguments de lancement
```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml
```

### 4. Configuration Eclipse - Méthode 2 (Classpath direct)

Modifier `.classpath` :

```xml
<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-11"/>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="lib" path="lib/sqlite-jdbc-********.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.base.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.controls.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.fxml.jar"/>
	<classpathentry kind="lib" path="C:/javafx-sdk-17.0.2/lib/javafx.graphics.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
```

### 5. Arguments VM Corrects

Dans Run Configuration > Arguments > VM arguments :

**Option 1 (Recommandée) :**
```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml
```

**Option 2 (Si problème de modules) :**
```
-Djava.library.path="C:\javafx-sdk-17.0.2\lib" --module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml,javafx.graphics,javafx.base
```

**Option 3 (Mode legacy) :**
```
--add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED
```

### 6. Test de Validation

Créer et lancer ce test simple :

```java
package com.drawingapp;

public class TestJavaFXModules {
    public static void main(String[] args) {
        try {
            Class.forName("javafx.application.Application");
            System.out.println("✅ JavaFX Application trouvé");
            
            Class.forName("javafx.scene.Scene");
            System.out.println("✅ JavaFX Scene trouvé");
            
            Class.forName("javafx.stage.Stage");
            System.out.println("✅ JavaFX Stage trouvé");
            
            System.out.println("✅ Tous les modules JavaFX sont disponibles !");
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ Module JavaFX manquant : " + e.getMessage());
        }
    }
}
```

### 7. Alternative : Java avec JavaFX Intégré

Si les problèmes persistent, utiliser une distribution Java avec JavaFX :

#### Liberica JDK Full
- **Télécharger** : https://bell-sw.com/pages/downloads/
- **Choisir** : Full version (avec JavaFX)
- **Installer** et configurer comme JRE par défaut

#### Azul Zulu FX
- **Télécharger** : https://www.azul.com/downloads/
- **Choisir** : Zulu FX (avec JavaFX)

### 8. Dépannage Avancé

#### Vérifier la version Java
```bash
java -version
javac -version
```

#### Lister les modules disponibles
```bash
java --list-modules | findstr javafx
```

#### Test module path
```bash
java --module-path "C:\javafx-sdk-17.0.2\lib" --list-modules | findstr javafx
```

### 9. Configuration Finale Recommandée

**Run Configuration dans Eclipse :**
- **Main class** : `com.drawingapp.Main`
- **VM arguments** :
```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED
```

## Résultat Attendu

Après configuration correcte :
- Aucune erreur de module
- L'application JavaFX se lance
- Interface de dessin fonctionnelle
