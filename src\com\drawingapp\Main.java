package com.drawingapp;

import com.drawingapp.controller.DrawingController;
import com.drawingapp.model.database.DatabaseManager;
import com.drawingapp.view.DrawingView;
import javafx.application.Application;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * Classe principale de l'application JavaFX de dessin
 * Point d'entrée de l'application
 */
public class Main extends Application {
    
    private static final String APP_TITLE = "Application de Dessin - Design Patterns";
    private static final int WINDOW_WIDTH = 1200;
    private static final int WINDOW_HEIGHT = 800;
    
    @Override
    public void start(Stage primaryStage) {
        try {
            // Initialisation de la base de données
            DatabaseManager.getInstance().initializeDatabase();
            
            // Création de la vue
            DrawingView view = new DrawingView();
            
            // Création du contrôleur
            DrawingController controller = new DrawingController(view);
            
            // Configuration de la scène
            Scene scene = new Scene(view.getRoot(), WINDOW_WIDTH, WINDOW_HEIGHT);
            
            // Configuration de la fenêtre principale
            primaryStage.setTitle(APP_TITLE);
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            primaryStage.show();
            
            // Gestion de la fermeture de l'application
            primaryStage.setOnCloseRequest(event -> {
                DatabaseManager.getInstance().closeConnection();
                System.exit(0);
            });
            
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Erreur lors du démarrage de l'application: " + e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
