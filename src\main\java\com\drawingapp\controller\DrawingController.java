package com.drawingapp.controller;

import com.drawingapp.model.drawing.Drawing;
import com.drawingapp.model.drawing.DrawingService;
import com.drawingapp.model.shapes.Shape;
import com.drawingapp.patterns.factory.ShapeFactory;
import com.drawingapp.patterns.observer.DrawingObserver;
import com.drawingapp.patterns.strategy.*;
import com.drawingapp.view.DrawingView;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.Alert;
import javafx.scene.input.MouseEvent;
import javafx.scene.paint.Color;

import java.sql.SQLException;
import java.util.List;

/**
 * Contrôleur principal de l'application de dessin
 * Implémente le pattern MVC et gère les interactions utilisateur
 */
public class DrawingController implements DrawingObserver {
    
    private DrawingView view;
    private Drawing currentDrawing;
    private DrawingService drawingService;
    private LoggingStrategy loggingStrategy;
    
    // Variables pour le dessin en cours
    private double startX, startY;
    private boolean isDrawing = false;
    
    public DrawingController(DrawingView view) {
        this.view = view;
        this.currentDrawing = new Drawing();
        this.drawingService = new DrawingService();
        
        // Initialisation de la stratégie de logging par défaut
        this.loggingStrategy = new ConsoleLoggingStrategy();
        this.loggingStrategy.initialize();
        
        // Enregistrement comme observateur
        this.currentDrawing.addObserver(this);
        
        // Configuration des événements
        setupEventHandlers();
        
        // Chargement initial des dessins sauvegardés
        refreshSavedDrawings();
        
        log("Application de dessin initialisée");
    }
    
    private void setupEventHandlers() {
        Canvas canvas = view.getCanvas();
        
        // Événements de la souris pour le dessin
        canvas.setOnMousePressed(this::handleMousePressed);
        canvas.setOnMouseDragged(this::handleMouseDragged);
        canvas.setOnMouseReleased(this::handleMouseReleased);
        
        // Événements des boutons
        view.getClearButton().setOnAction(e -> clearDrawing());
        view.getSaveButton().setOnAction(e -> saveDrawing());
        view.getLoadButton().setOnAction(e -> loadDrawing());
        view.getRefreshButton().setOnAction(e -> refreshSavedDrawings());
        
        // Changement de stratégie de logging
        view.getLoggingStrategyCombo().setOnAction(e -> changeLoggingStrategy());
    }
    
    private void handleMousePressed(MouseEvent event) {
        startX = event.getX();
        startY = event.getY();
        isDrawing = true;
        
        log(String.format("Début du dessin à (%.1f, %.1f)", startX, startY));
    }
    
    private void handleMouseDragged(MouseEvent event) {
        if (!isDrawing) return;
        
        // Prévisualisation en temps réel
        redrawCanvas();
        drawPreview(event.getX(), event.getY());
    }
    
    private void handleMouseReleased(MouseEvent event) {
        if (!isDrawing) return;
        
        double endX = event.getX();
        double endY = event.getY();
        
        // Création de la forme finale
        ShapeFactory.ShapeType shapeType = view.getSelectedShapeType();
        Shape shape = ShapeFactory.createShape(shapeType, startX, startY, endX, endY);
        
        // Configuration de la forme
        shape.setColor(view.getColorPicker().getValue());
        shape.setStrokeWidth(view.getStrokeWidthSlider().getValue());
        
        // Ajout au dessin
        currentDrawing.addShape(shape);
        
        // Redessin complet
        redrawCanvas();
        
        isDrawing = false;
        
        log(String.format("Forme %s créée de (%.1f, %.1f) à (%.1f, %.1f)", 
                         shapeType, startX, startY, endX, endY));
    }
    
    private void drawPreview(double currentX, double currentY) {
        GraphicsContext gc = view.getGraphicsContext();
        
        // Sauvegarde du contexte
        gc.save();
        
        // Configuration pour la prévisualisation
        gc.setStroke(view.getColorPicker().getValue());
        gc.setLineWidth(view.getStrokeWidthSlider().getValue());
        gc.getLineDashes().clear();
        gc.setLineDashes(5); // Ligne pointillée pour la prévisualisation
        
        // Dessin de la prévisualisation
        ShapeFactory.ShapeType shapeType = view.getSelectedShapeType();
        switch (shapeType) {
            case RECTANGLE:
                double x = Math.min(startX, currentX);
                double y = Math.min(startY, currentY);
                double width = Math.abs(currentX - startX);
                double height = Math.abs(currentY - startY);
                gc.strokeRect(x, y, width, height);
                break;
            case CIRCLE:
                double radius = Math.sqrt(Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2));
                gc.strokeOval(startX - radius, startY - radius, radius * 2, radius * 2);
                break;
            case LINE:
                gc.strokeLine(startX, startY, currentX, currentY);
                break;
        }
        
        // Restauration du contexte
        gc.restore();
    }
    
    private void redrawCanvas() {
        view.clearCanvas();
        
        // Redessiner toutes les formes
        GraphicsContext gc = view.getGraphicsContext();
        for (Shape shape : currentDrawing.getShapes()) {
            shape.render(gc);
        }
    }
    
    private void clearDrawing() {
        currentDrawing.clear();
        view.clearCanvas();
        log("Dessin effacé");
    }
    
    private void saveDrawing() {
        String drawingName = view.getDrawingNameField().getText().trim();
        
        if (drawingName.isEmpty()) {
            showAlert("Erreur", "Veuillez saisir un nom pour le dessin.");
            return;
        }
        
        if (currentDrawing.getShapeCount() == 0) {
            showAlert("Avertissement", "Le dessin est vide.");
            return;
        }
        
        try {
            currentDrawing.setName(drawingName);
            drawingService.saveDrawing(currentDrawing);
            currentDrawing.notifyDrawingSaved(drawingName);
            refreshSavedDrawings();
            showAlert("Succès", "Dessin sauvegardé avec succès.");
        } catch (SQLException e) {
            showAlert("Erreur", "Erreur lors de la sauvegarde: " + e.getMessage());
            log("Erreur de sauvegarde: " + e.getMessage());
        }
    }
    
    private void loadDrawing() {
        String selectedDrawing = view.getSavedDrawingsCombo().getValue();
        
        if (selectedDrawing == null || selectedDrawing.isEmpty()) {
            showAlert("Erreur", "Veuillez sélectionner un dessin à charger.");
            return;
        }
        
        try {
            Drawing loadedDrawing = drawingService.loadDrawing(selectedDrawing);
            if (loadedDrawing != null) {
                currentDrawing = loadedDrawing;
                currentDrawing.addObserver(this);
                redrawCanvas();
                currentDrawing.notifyDrawingLoaded(selectedDrawing);
                view.getDrawingNameField().setText(selectedDrawing);
                showAlert("Succès", "Dessin chargé avec succès.");
            } else {
                showAlert("Erreur", "Dessin non trouvé.");
            }
        } catch (SQLException e) {
            showAlert("Erreur", "Erreur lors du chargement: " + e.getMessage());
            log("Erreur de chargement: " + e.getMessage());
        }
    }
    
    private void refreshSavedDrawings() {
        try {
            List<String> drawingNames = drawingService.getDrawingNames();
            view.getSavedDrawingsCombo().getItems().clear();
            view.getSavedDrawingsCombo().getItems().addAll(drawingNames);
            log("Liste des dessins actualisée (" + drawingNames.size() + " dessins)");
        } catch (SQLException e) {
            showAlert("Erreur", "Erreur lors de l'actualisation: " + e.getMessage());
            log("Erreur d'actualisation: " + e.getMessage());
        }
    }
    
    private void changeLoggingStrategy() {
        String selectedStrategy = view.getLoggingStrategyCombo().getValue();
        
        // Fermeture de l'ancienne stratégie
        if (loggingStrategy != null) {
            loggingStrategy.close();
        }
        
        // Création de la nouvelle stratégie
        switch (selectedStrategy) {
            case "Console":
                loggingStrategy = new ConsoleLoggingStrategy();
                break;
            case "Fichier":
                loggingStrategy = new FileLoggingStrategy();
                break;
            case "Base de données":
                loggingStrategy = new DatabaseLoggingStrategy();
                break;
            default:
                loggingStrategy = new ConsoleLoggingStrategy();
        }
        
        loggingStrategy.initialize();
        log("Stratégie de journalisation changée vers: " + selectedStrategy);
    }
    
    private void log(String message) {
        if (loggingStrategy != null) {
            loggingStrategy.log(message);
        }
        view.addLogMessage(message);
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Implémentation de DrawingObserver
    @Override
    public void onShapeAdded(Shape shape) {
        log("Forme ajoutée: " + shape.toString());
    }
    
    @Override
    public void onDrawingCleared() {
        log("Dessin effacé");
    }
    
    @Override
    public void onDrawingLoaded(String drawingName) {
        log("Dessin chargé: " + drawingName);
    }
    
    @Override
    public void onDrawingSaved(String drawingName) {
        log("Dessin sauvegardé: " + drawingName);
    }
}
