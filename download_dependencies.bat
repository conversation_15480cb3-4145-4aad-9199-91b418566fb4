@echo off
echo Téléchargement des dépendances JavaFX et SQLite...

if not exist lib mkdir lib

echo Téléchargement de SQLite JDBC...
curl -L -o lib/sqlite-jdbc-********.jar https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar

echo.
echo Dépendances téléchargées avec succès !
echo.
echo IMPORTANT: Pour JavaFX, vous devez :
echo 1. Télécharger JavaFX SDK depuis https://openjfx.io/
echo 2. Extraire le SDK dans un dossier (ex: C:\javafx-sdk-17.0.2)
echo 3. Dans Eclipse, aller dans Window ^> Preferences ^> Java ^> Build Path ^> User Libraries
echo 4. Créer une nouvelle User Library appelée "JavaFX"
echo 5. Ajouter tous les JAR du dossier javafx-sdk-17.0.2\lib
echo.
echo Ensuite, vous pourrez compiler et exécuter le projet !
pause
