package com.drawingapp.patterns.observer;

import com.drawingapp.model.shapes.Shape;

/**
 * Interface Observer pour être notifié des changements dans le dessin
 */
public interface DrawingObserver {
    
    /**
     * Notifie qu'une forme a été ajoutée au dessin
     * @param shape Forme ajoutée
     */
    void onShapeAdded(Shape shape);
    
    /**
     * Notifie que le dessin a été effacé
     */
    void onDrawingCleared();
    
    /**
     * Notifie qu'un dessin a été chargé
     * @param drawingName Nom du dessin chargé
     */
    void onDrawingLoaded(String drawingName);
    
    /**
     * Notifie qu'un dessin a été sauvegardé
     * @param drawingName Nom du dessin sauvegardé
     */
    void onDrawingSaved(String drawingName);
}
