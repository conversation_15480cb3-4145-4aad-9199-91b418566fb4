@echo off
echo ========================================
echo   Vérification de JavaFX SDK
echo ========================================
echo.

REM Chemins possibles de JavaFX
set JAVAFX_PATH1=C:\javafx-sdk-17.0.2\lib
set JAVAFX_PATH2=C:\javafx-sdk-19\lib
set JAVAFX_PATH3=C:\javafx-sdk-21\lib
set JAVAFX_PATH4=C:\Program Files\Java\javafx-sdk-17.0.2\lib

echo Recherche de JavaFX SDK...
echo.

REM Vérifier chaque chemin possible
if exist "%JAVAFX_PATH1%" (
    echo ✅ JavaFX trouvé dans: %JAVAFX_PATH1%
    set JAVAFX_PATH=%JAVAFX_PATH1%
    goto :check_content
)

if exist "%JAVAFX_PATH2%" (
    echo ✅ JavaFX trouvé dans: %JAVAFX_PATH2%
    set JAVAFX_PATH=%JAVAFX_PATH2%
    goto :check_content
)

if exist "%JAVAFX_PATH3%" (
    echo ✅ JavaFX trouvé dans: %JAVAFX_PATH3%
    set JAVAFX_PATH=%JAVAFX_PATH3%
    goto :check_content
)

if exist "%JAVAFX_PATH4%" (
    echo ✅ JavaFX trouvé dans: %JAVAFX_PATH4%
    set JAVAFX_PATH=%JAVAFX_PATH4%
    goto :check_content
)

echo ❌ JavaFX SDK non trouvé !
echo.
echo SOLUTION:
echo 1. Télécharger JavaFX SDK depuis https://openjfx.io/
echo 2. Extraire dans C:\javafx-sdk-17.0.2
echo 3. Relancer ce script
echo.
pause
exit /b 1

:check_content
echo.
echo Vérification du contenu de JavaFX...

if exist "%JAVAFX_PATH%\javafx.base.jar" (
    echo ✅ javafx.base.jar
) else (
    echo ❌ javafx.base.jar MANQUANT
    set ERROR=1
)

if exist "%JAVAFX_PATH%\javafx.controls.jar" (
    echo ✅ javafx.controls.jar
) else (
    echo ❌ javafx.controls.jar MANQUANT
    set ERROR=1
)

if exist "%JAVAFX_PATH%\javafx.fxml.jar" (
    echo ✅ javafx.fxml.jar
) else (
    echo ❌ javafx.fxml.jar MANQUANT
    set ERROR=1
)

if exist "%JAVAFX_PATH%\javafx.graphics.jar" (
    echo ✅ javafx.graphics.jar
) else (
    echo ❌ javafx.graphics.jar MANQUANT
    set ERROR=1
)

if defined ERROR (
    echo.
    echo ❌ Installation JavaFX incomplète !
    echo Retéléchargez JavaFX SDK complet depuis https://openjfx.io/
    pause
    exit /b 1
)

echo.
echo ✅ JavaFX SDK complet et valide !
echo Chemin: %JAVAFX_PATH%
echo.
echo Vous pouvez maintenant utiliser ce chemin dans Eclipse :
echo --module-path "%JAVAFX_PATH%" --add-modules javafx.controls,javafx.fxml
echo.
pause
