package com.drawingapp.model.shapes;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Classe abstraite représentant une forme géométrique
 * Utilise le pattern Template Method pour définir la structure commune
 */
public abstract class Shape {
    protected double startX, startY, endX, endY;
    protected Color color;
    protected double strokeWidth;
    protected String shapeType;
    
    public Shape(double startX, double startY, double endX, double endY) {
        this.startX = startX;
        this.startY = startY;
        this.endX = endX;
        this.endY = endY;
        this.color = Color.BLACK;
        this.strokeWidth = 2.0;
    }
    
    // Template Method Pattern
    public final void render(GraphicsContext gc) {
        setupGraphicsContext(gc);
        drawShape(gc);
    }
    
    protected void setupGraphicsContext(GraphicsContext gc) {
        gc.setStroke(color);
        gc.setLineWidth(strokeWidth);
    }
    
    // Méthode abstraite à implémenter par les sous-classes
    protected abstract void drawShape(GraphicsContext gc);
    
    // Getters et Setters
    public double getStartX() { return startX; }
    public void setStartX(double startX) { this.startX = startX; }
    
    public double getStartY() { return startY; }
    public void setStartY(double startY) { this.startY = startY; }
    
    public double getEndX() { return endX; }
    public void setEndX(double endX) { this.endX = endX; }
    
    public double getEndY() { return endY; }
    public void setEndY(double endY) { this.endY = endY; }
    
    public Color getColor() { return color; }
    public void setColor(Color color) { this.color = color; }
    
    public double getStrokeWidth() { return strokeWidth; }
    public void setStrokeWidth(double strokeWidth) { this.strokeWidth = strokeWidth; }
    
    public String getShapeType() { return shapeType; }
    
    @Override
    public String toString() {
        return String.format("%s: (%.1f,%.1f) to (%.1f,%.1f)", 
                           shapeType, startX, startY, endX, endY);
    }
}
