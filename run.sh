#!/bin/bash
echo "Lancement de l'application de dessin..."

# Vérifier si les classes sont compilées
if [ ! -f "bin/com/drawingapp/Main.class" ]; then
    echo "Compilation des sources..."
    mkdir -p bin
    javac -cp "lib/*" -d bin src/com/drawingapp/**/*.java
    if [ $? -ne 0 ]; then
        echo "Erreur de compilation !"
        exit 1
    fi
fi

# Définir le chemin JavaFX (à modifier selon votre installation)
JAVAFX_PATH="/opt/javafx-sdk-17.0.2/lib"

# Vérifier si JavaFX est disponible
if [ ! -d "$JAVAFX_PATH" ]; then
    echo "ERREUR: JavaFX SDK non trouvé dans $JAVAFX_PATH"
    echo "Veuillez :"
    echo "1. Télécharger JavaFX SDK depuis https://openjfx.io/"
    echo "2. Modifier la variable JAVAFX_PATH dans ce script"
    exit 1
fi

echo "Démarrage de l'application..."
java -cp "bin:lib/*" --module-path "$JAVAFX_PATH" --add-modules javafx.controls,javafx.fxml com.drawingapp.Main
