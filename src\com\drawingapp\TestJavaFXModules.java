package com.drawingapp;

/**
 * Test pour vérifier que les modules JavaFX sont disponibles
 * À lancer AVANT TestJavaFX pour diagnostiquer les problèmes
 */
public class TestJavaFXModules {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("  Test des Modules JavaFX");
        System.out.println("========================================");
        System.out.println();
        
        // Test 1 : Vérifier Java version
        System.out.println("1. Version Java :");
        System.out.println("   " + System.getProperty("java.version"));
        System.out.println("   " + System.getProperty("java.vendor"));
        System.out.println();
        
        // Test 2 : Vérifier module path
        System.out.println("2. Module Path :");
        String modulePath = System.getProperty("jdk.module.path");
        if (modulePath != null) {
            System.out.println("   " + modulePath);
        } else {
            System.out.println("   ❌ Module path non défini");
        }
        System.out.println();
        
        // Test 3 : Tester les classes JavaFX
        System.out.println("3. Test des classes JavaFX :");
        
        testClass("javafx.application.Application", "JavaFX Application");
        testClass("javafx.stage.Stage", "JavaFX Stage");
        testClass("javafx.scene.Scene", "JavaFX Scene");
        testClass("javafx.scene.control.Button", "JavaFX Controls");
        testClass("javafx.scene.layout.VBox", "JavaFX Layouts");
        testClass("javafx.scene.canvas.Canvas", "JavaFX Canvas");
        testClass("javafx.scene.paint.Color", "JavaFX Paint");
        
        System.out.println();
        System.out.println("========================================");
        System.out.println("  Résultat du Test");
        System.out.println("========================================");
        
        if (allTestsPassed) {
            System.out.println("✅ SUCCÈS : Tous les modules JavaFX sont disponibles !");
            System.out.println("   Vous pouvez maintenant lancer TestJavaFX.java");
        } else {
            System.out.println("❌ ÉCHEC : Certains modules JavaFX sont manquants");
            System.out.println();
            System.out.println("SOLUTIONS :");
            System.out.println("1. Vérifier que JavaFX SDK est téléchargé");
            System.out.println("2. Configurer les arguments VM :");
            System.out.println("   --module-path \"C:\\javafx-sdk-17.0.2\\lib\" --add-modules javafx.controls,javafx.fxml");
            System.out.println("3. Ou utiliser une distribution Java avec JavaFX intégré");
        }
        
        System.out.println();
    }
    
    private static boolean allTestsPassed = true;
    
    private static void testClass(String className, String description) {
        try {
            Class.forName(className);
            System.out.println("   ✅ " + description + " (" + className + ")");
        } catch (ClassNotFoundException e) {
            System.out.println("   ❌ " + description + " (" + className + ") - MANQUANT");
            allTestsPassed = false;
        }
    }
}
