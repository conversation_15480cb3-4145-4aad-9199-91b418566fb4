@echo off
echo ========================================
echo Configuration JavaFX pour Eclipse
echo ========================================
echo.

REM Vérifier si JavaFX SDK existe
set JAVAFX_PATH=C:\javafx-sdk-17.0.2
if exist "%JAVAFX_PATH%" (
    echo ✅ JavaFX SDK trouvé dans %JAVAFX_PATH%
) else (
    echo ❌ JavaFX SDK non trouvé dans %JAVAFX_PATH%
    echo.
    echo ÉTAPES À SUIVRE :
    echo 1. Télécharger JavaFX SDK depuis https://openjfx.io/
    echo 2. Extraire dans C:\javafx-sdk-17.0.2
    echo 3. Relancer ce script
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Configuration Eclipse
echo ========================================
echo.
echo ÉTAPES MANUELLES DANS ECLIPSE :
echo.
echo 1. Window ^> Preferences ^> Java ^> Build Path ^> User Libraries
echo 2. New... ^> Nom: JavaFX
echo 3. Add External JARs... ^> Sélectionner tous les JARs de %JAVAFX_PATH%\lib\
echo 4. OK ^> Apply and Close
echo.
echo 5. Clic droit sur projet ^> Properties ^> Java Build Path ^> Libraries
echo 6. Add Library... ^> User Library ^> JavaFX ^> Finish
echo.
echo 7. Clic droit sur Main.java ^> Run As ^> Run Configurations
echo 8. Arguments ^> VM arguments :
echo    --module-path "%JAVAFX_PATH%\lib" --add-modules javafx.controls,javafx.fxml
echo.
echo ========================================
echo Test de Configuration
echo ========================================
echo.
echo Pour tester la configuration :
echo 1. Lancer TestJavaFX.java d'abord
echo 2. Si ça marche, lancer Main.java
echo.

pause
