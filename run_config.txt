Configuration pour Eclipse - Arguments de lancement
=====================================================

Pour configurer l'exécution dans Eclipse :

1. Clic droit sur Main.java > Run As > Run Configurations
2. Dans l'onglet "Arguments", section "VM arguments", ajouter :

Windows :
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml

Linux/Mac :
--module-path "/opt/javafx-sdk-17.0.2/lib" --add-modules javafx.controls,javafx.fxml

Remplacez le chemin par l'emplacement de votre installation JavaFX SDK.

Alternative pour les systèmes avec JavaFX intégré :
--add-modules javafx.controls,javafx.fxml

Note : Si vous utilisez Java 8 avec JavaFX intégré, ces arguments ne sont pas nécessaires.
