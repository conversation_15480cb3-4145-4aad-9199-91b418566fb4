@echo off
title Compilation et Lancement - Application de Dessin
echo ========================================
echo  Compilation et Lancement Automatique
echo ========================================
echo.

REM Configuration
set JAVAFX_PATH=C:\javafx-sdk-17.0.2\lib
set PROJECT_PATH=%~dp0

echo Étape 1: Vérification de JavaFX...
if not exist "%JAVAFX_PATH%" (
    echo ❌ JavaFX SDK non trouvé dans %JAVAFX_PATH%
    echo Veuillez télécharger JavaFX SDK depuis https://openjfx.io/
    pause
    exit /b 1
)
echo ✅ JavaFX SDK trouvé

echo.
echo Étape 2: Nettoyage et création des dossiers...
if exist bin rmdir /s /q bin
mkdir bin 2>nul

echo.
echo Étape 3: Compilation du projet...
javac -cp "lib\*;%JAVAFX_PATH%\*" -d bin src\com\drawingapp\*.java src\com\drawingapp\**\*.java

if errorlevel 1 (
    echo ❌ Erreur de compilation !
    echo Vérifiez que Java 11+ est installé et configuré
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo.
echo Étape 4: Test JavaFX...
echo Lancement du test JavaFX...
java -cp "bin;lib\*" ^
     --module-path "%JAVAFX_PATH%" ^
     --add-modules javafx.controls,javafx.fxml,javafx.graphics,javafx.base ^
     --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     com.drawingapp.TestJavaFX

if errorlevel 1 (
    echo ❌ Test JavaFX échoué !
    echo Vérifiez la configuration JavaFX
    pause
    exit /b 1
)

echo.
echo ✅ Test JavaFX réussi !
echo.
echo Étape 5: Lancement de l'application principale...
timeout /t 3 /nobreak >nul

java -cp "bin;lib\*" ^
     --module-path "%JAVAFX_PATH%" ^
     --add-modules javafx.controls,javafx.fxml,javafx.graphics,javafx.base ^
     --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     com.drawingapp.Main

echo.
echo Application fermée.
pause
