package com.drawingapp.patterns.strategy;

/**
 * Interface Strategy pour les différentes stratégies de journalisation
 */
public interface LoggingStrategy {
    
    /**
     * Enregistre un message selon la stratégie implémentée
     * @param message Message à enregistrer
     */
    void log(String message);
    
    /**
     * Initialise la stratégie de logging
     */
    void initialize();
    
    /**
     * Ferme les ressources utilisées par la stratégie
     */
    void close();
    
    /**
     * Retourne le nom de la stratégie
     * @return Nom de la stratégie
     */
    String getStrategyName();
}
