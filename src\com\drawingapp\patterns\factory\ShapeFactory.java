package com.drawingapp.patterns.factory;

import com.drawingapp.model.shapes.*;

/**
 * Factory Pattern pour créer les différentes formes géométriques
 * Centralise la création des objets Shape
 */
public class ShapeFactory {
    
    public enum ShapeType {
        RECTANGLE, CIRCLE, LINE
    }
    
    /**
     * Crée une forme selon le type spécifié
     * @param type Type de forme à créer
     * @param startX Coordonnée X de départ
     * @param startY Coordonnée Y de départ
     * @param endX Coordonnée X de fin
     * @param endY Coordonnée Y de fin
     * @return Instance de la forme créée
     */
    public static Shape createShape(ShapeType type, double startX, double startY, double endX, double endY) {
        switch (type) {
            case RECTANGLE:
                return new Rectangle(startX, startY, endX, endY);
            case CIRCLE:
                return new Circle(startX, startY, endX, endY);
            case LINE:
                return new Line(startX, startY, endX, endY);
            default:
                throw new IllegalArgumentException("Type de forme non supporté: " + type);
        }
    }
    
    /**
     * Crée une forme à partir d'une chaîne de caractères
     * @param shapeTypeName Nom du type de forme
     * @param startX Coordonnée X de départ
     * @param startY Coordonnée Y de départ
     * @param endX Coordonnée X de fin
     * @param endY Coordonnée Y de fin
     * @return Instance de la forme créée
     */
    public static Shape createShape(String shapeTypeName, double startX, double startY, double endX, double endY) {
        try {
            ShapeType type = ShapeType.valueOf(shapeTypeName.toUpperCase());
            return createShape(type, startX, startY, endX, endY);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Type de forme non reconnu: " + shapeTypeName);
        }
    }
}
