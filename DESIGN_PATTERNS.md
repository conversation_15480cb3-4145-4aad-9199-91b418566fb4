# Design Patterns Implémentés

## Vue d'ensemble

Cette application démontre l'utilisation de 6 design patterns principaux pour créer une architecture robuste et extensible.

## 1. Factory Pattern

**Fichier** : `src/com/drawingapp/patterns/factory/ShapeFactory.java`

**Objectif** : Centraliser la création des objets Shape

**Utilisation** :
```java
Shape rectangle = ShapeFactory.createShape(ShapeType.RECTANGLE, x1, y1, x2, y2);
Shape circle = ShapeFactory.createShape(ShapeType.CIRCLE, x1, y1, x2, y2);
```

**Avantages** :
- Facilite l'ajout de nouvelles formes
- Centralise la logique de création
- Respecte le principe Open/Closed

## 2. Strategy Pattern

**Fichiers** :
- `src/com/drawingapp/patterns/strategy/LoggingStrategy.java` (interface)
- `src/com/drawingapp/patterns/strategy/ConsoleLoggingStrategy.java`
- `src/com/drawingapp/patterns/strategy/FileLoggingStrategy.java`
- `src/com/drawingapp/patterns/strategy/DatabaseLoggingStrategy.java`

**Objectif** : Permettre le changement dynamique de stratégie de journalisation

**Utilisation** :
```java
LoggingStrategy strategy = new ConsoleLoggingStrategy();
strategy.log("Message");

// Changement de stratégie
strategy = new FileLoggingStrategy();
strategy.log("Message");
```

**Avantages** :
- Changement de comportement à l'exécution
- Facilite l'ajout de nouvelles stratégies
- Découplage entre client et implémentation

## 3. Observer Pattern

**Fichiers** :
- `src/com/drawingapp/patterns/observer/DrawingObserver.java` (interface)
- `src/com/drawingapp/model/drawing/Drawing.java` (Subject)
- `src/com/drawingapp/controller/DrawingController.java` (Observer)

**Objectif** : Notifier les changements dans le modèle

**Utilisation** :
```java
drawing.addObserver(controller);
drawing.addShape(shape); // Notifie automatiquement les observateurs
```

**Avantages** :
- Découplage entre modèle et vue
- Notification automatique des changements
- Facilite l'ajout de nouveaux observateurs

## 4. Singleton Pattern

**Fichier** : `src/com/drawingapp/model/database/DatabaseManager.java`

**Objectif** : Assurer une seule instance de connexion à la base de données

**Utilisation** :
```java
DatabaseManager dbManager = DatabaseManager.getInstance();
Connection conn = dbManager.getConnection();
```

**Avantages** :
- Une seule connexion partagée
- Contrôle de l'accès aux ressources
- Économie de mémoire

## 5. Template Method Pattern

**Fichier** : `src/com/drawingapp/model/shapes/Shape.java`

**Objectif** : Définir la structure commune du rendu des formes

**Utilisation** :
```java
public abstract class Shape {
    public final void render(GraphicsContext gc) {
        setupGraphicsContext(gc);  // Commun à toutes les formes
        drawShape(gc);             // Spécifique à chaque forme
    }
    
    protected abstract void drawShape(GraphicsContext gc);
}
```

**Avantages** :
- Code réutilisable
- Structure cohérente
- Personnalisation par sous-classe

## 6. MVC Pattern

**Fichiers** :
- **Model** : `src/com/drawingapp/model/`
- **View** : `src/com/drawingapp/view/DrawingView.java`
- **Controller** : `src/com/drawingapp/controller/DrawingController.java`

**Objectif** : Séparer les responsabilités

**Structure** :
- **Model** : Gestion des données (Drawing, Shape, DatabaseManager)
- **View** : Interface utilisateur (DrawingView)
- **Controller** : Logique métier et coordination (DrawingController)

**Avantages** :
- Séparation claire des responsabilités
- Facilite la maintenance
- Permet la réutilisation des composants

## Interactions entre les Patterns

### Flux typique d'utilisation :

1. **L'utilisateur** dessine une forme dans la **View**
2. **Le Controller** reçoit l'événement
3. **Factory Pattern** crée la forme appropriée
4. **Observer Pattern** notifie les changements
5. **Strategy Pattern** journalise l'action
6. **Singleton Pattern** gère la persistance
7. **Template Method** assure le rendu cohérent

### Exemple concret :

```java
// 1. Utilisateur dessine un rectangle
// 2. Controller reçoit l'événement
public void handleMouseReleased(MouseEvent event) {
    // 3. Factory crée la forme
    Shape shape = ShapeFactory.createShape(RECTANGLE, x1, y1, x2, y2);
    
    // 4. Observer notifie
    currentDrawing.addShape(shape); // Déclenche onShapeAdded()
    
    // 5. Strategy journalise
    loggingStrategy.log("Rectangle créé");
    
    // 6. Singleton pour la persistance (si sauvegarde)
    DatabaseManager.getInstance().saveDrawing(drawing);
    
    // 7. Template Method pour le rendu
    shape.render(graphicsContext);
}
```

## Extensibilité

### Ajouter une nouvelle forme :
1. Créer une classe héritant de `Shape`
2. Ajouter le type dans `ShapeFactory`
3. Aucune modification ailleurs nécessaire

### Ajouter une nouvelle stratégie de logging :
1. Implémenter `LoggingStrategy`
2. Ajouter dans la liste des stratégies
3. Aucune modification du code client

### Ajouter un nouvel observateur :
1. Implémenter `DrawingObserver`
2. S'enregistrer auprès du `Drawing`
3. Recevoir automatiquement les notifications

## Conclusion

Cette architecture démontre comment les design patterns peuvent être combinés pour créer une application :
- **Modulaire** : Chaque pattern a sa responsabilité
- **Extensible** : Facile d'ajouter de nouvelles fonctionnalités
- **Maintenable** : Code organisé et découplé
- **Réutilisable** : Composants indépendants
