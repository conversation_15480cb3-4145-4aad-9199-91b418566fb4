package com.drawingapp.model.shapes;

import javafx.scene.canvas.GraphicsContext;

/**
 * Implémentation concrète d'une ligne
 */
public class Line extends Shape {
    
    public Line(double startX, double startY, double endX, double endY) {
        super(startX, startY, endX, endY);
        this.shapeType = "Line";
    }
    
    @Override
    protected void drawShape(GraphicsContext gc) {
        gc.strokeLine(startX, startY, endX, endY);
    }
}
