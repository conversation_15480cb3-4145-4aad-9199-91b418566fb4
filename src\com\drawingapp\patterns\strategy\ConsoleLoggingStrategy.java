package com.drawingapp.patterns.strategy;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Stratégie de journalisation dans la console
 */
public class ConsoleLoggingStrategy implements LoggingStrategy {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void log(String message) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        System.out.println("[" + timestamp + "] " + message);
    }
    
    @Override
    public void initialize() {
        log("Initialisation de la journalisation console");
    }
    
    @Override
    public void close() {
        log("Fermeture de la journalisation console");
    }
    
    @Override
    public String getStrategyName() {
        return "Console";
    }
}
