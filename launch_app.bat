@echo off
title Application de Dessin JavaFX
echo ========================================
echo    Application de Dessin JavaFX
echo ========================================
echo.

REM Configuration des chemins
set JAVAFX_PATH=C:\javafx-sdk-17.0.2\lib
set PROJECT_PATH=%~dp0
set BIN_PATH=%PROJECT_PATH%bin
set LIB_PATH=%PROJECT_PATH%lib

echo Vérification de la configuration...

REM Vérifier JavaFX
if not exist "%JAVAFX_PATH%" (
    echo ❌ ERREUR: JavaFX SDK non trouvé dans %JAVAFX_PATH%
    echo.
    echo SOLUTION:
    echo 1. Télécharger JavaFX SDK depuis https://openjfx.io/
    echo 2. Extraire dans C:\javafx-sdk-17.0.2
    echo 3. Ou modifier JAVAFX_PATH dans ce script
    echo.
    pause
    exit /b 1
)

REM Vérifier compilation
if not exist "%BIN_PATH%\com\drawingapp\Main.class" (
    echo ❌ ERREUR: Projet non compilé
    echo.
    echo SOLUTION:
    echo 1. Ouvrir le projet dans Eclipse
    echo 2. Project ^> Clean ^> Clean all projects
    echo 3. Ou compiler manuellement avec compile.bat
    echo.
    pause
    exit /b 1
)

REM Vérifier SQLite
if not exist "%LIB_PATH%\sqlite-jdbc-********.jar" (
    echo ❌ ERREUR: SQLite JDBC manquant
    echo.
    echo SOLUTION:
    echo 1. Exécuter download_dependencies.bat
    echo.
    pause
    exit /b 1
)

echo ✅ JavaFX SDK trouvé
echo ✅ Projet compilé
echo ✅ Dépendances présentes
echo.
echo Lancement de l'application...
echo.

REM Lancer l'application avec tous les modules nécessaires
java -cp "%BIN_PATH%;%LIB_PATH%\*" ^
     --module-path "%JAVAFX_PATH%" ^
     --add-modules javafx.controls,javafx.fxml,javafx.graphics,javafx.base ^
     --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     com.drawingapp.Main

if errorlevel 1 (
    echo.
    echo ❌ ERREUR lors du lancement !
    echo.
    echo VÉRIFICATIONS:
    echo 1. Java 11+ est installé
    echo 2. JavaFX SDK est compatible avec votre Java
    echo 3. Tous les modules JavaFX sont présents
    echo.
    echo Pour tester JavaFX, essayez d'abord TestJavaFX.java
    echo.
) else (
    echo.
    echo ✅ Application fermée normalement
)

pause
