package com.drawingapp.view;

import com.drawingapp.patterns.factory.ShapeFactory;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;

/**
 * Vue principale de l'application de dessin
 * Contient l'interface utilisateur complète
 */
public class DrawingView {
    
    private BorderPane root;
    private Canvas canvas;
    private GraphicsContext gc;
    
    // Contrôles de l'interface
    private ToggleGroup shapeToggleGroup;
    private RadioButton rectangleRadio, circleRadio, lineRadio;
    private ColorPicker colorPicker;
    private Slider strokeWidthSlider;
    private ComboBox<String> loggingStrategyCombo;
    private TextField drawingNameField;
    private ComboBox<String> savedDrawingsCombo;
    private TextArea logArea;
    
    // Boutons
    private Button clearButton, saveButton, loadButton, refreshButton;
    
    public DrawingView() {
        initializeComponents();
        setupLayout();
        setupStyling();
    }
    
    private void initializeComponents() {
        // Conteneur principal
        root = new BorderPane();
        
        // Zone de dessin
        canvas = new Canvas(800, 600);
        gc = canvas.getGraphicsContext2D();
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        
        // Groupe de boutons radio pour les formes
        shapeToggleGroup = new ToggleGroup();
        rectangleRadio = new RadioButton("Rectangle");
        circleRadio = new RadioButton("Cercle");
        lineRadio = new RadioButton("Ligne");
        
        rectangleRadio.setToggleGroup(shapeToggleGroup);
        circleRadio.setToggleGroup(shapeToggleGroup);
        lineRadio.setToggleGroup(shapeToggleGroup);
        rectangleRadio.setSelected(true); // Sélection par défaut
        
        // Sélecteur de couleur
        colorPicker = new ColorPicker(Color.BLACK);
        
        // Slider pour l'épaisseur du trait
        strokeWidthSlider = new Slider(1, 10, 2);
        strokeWidthSlider.setShowTickLabels(true);
        strokeWidthSlider.setShowTickMarks(true);
        strokeWidthSlider.setMajorTickUnit(2);
        
        // ComboBox pour la stratégie de logging
        loggingStrategyCombo = new ComboBox<>();
        loggingStrategyCombo.getItems().addAll("Console", "Fichier", "Base de données");
        loggingStrategyCombo.setValue("Console");
        
        // Champs pour la sauvegarde/chargement
        drawingNameField = new TextField();
        drawingNameField.setPromptText("Nom du dessin");
        
        savedDrawingsCombo = new ComboBox<>();
        savedDrawingsCombo.setPromptText("Dessins sauvegardés");
        
        // Zone de log
        logArea = new TextArea();
        logArea.setEditable(false);
        logArea.setPrefRowCount(8);
        
        // Boutons
        clearButton = new Button("Effacer");
        saveButton = new Button("Sauvegarder");
        loadButton = new Button("Charger");
        refreshButton = new Button("Actualiser");
    }
    
    private void setupLayout() {
        // Panneau de gauche - Outils
        VBox leftPanel = createLeftPanel();
        
        // Panneau central - Zone de dessin
        ScrollPane canvasScrollPane = new ScrollPane(canvas);
        canvasScrollPane.setFitToWidth(true);
        canvasScrollPane.setFitToHeight(true);
        
        // Panneau de droite - Logs
        VBox rightPanel = createRightPanel();
        
        // Assemblage
        root.setLeft(leftPanel);
        root.setCenter(canvasScrollPane);
        root.setRight(rightPanel);
    }
    
    private VBox createLeftPanel() {
        VBox leftPanel = new VBox(10);
        leftPanel.setPadding(new Insets(10));
        leftPanel.setPrefWidth(200);
        
        // Section formes
        Label shapesLabel = new Label("Formes géométriques");
        shapesLabel.setStyle("-fx-font-weight: bold;");
        
        VBox shapesBox = new VBox(5);
        shapesBox.getChildren().addAll(rectangleRadio, circleRadio, lineRadio);
        
        // Section couleur
        Label colorLabel = new Label("Couleur");
        colorLabel.setStyle("-fx-font-weight: bold;");
        
        // Section épaisseur
        Label strokeLabel = new Label("Épaisseur du trait");
        strokeLabel.setStyle("-fx-font-weight: bold;");
        Label strokeValueLabel = new Label("2.0");
        strokeWidthSlider.valueProperty().addListener((obs, oldVal, newVal) -> 
            strokeValueLabel.setText(String.format("%.1f", newVal.doubleValue())));
        
        // Section logging
        Label loggingLabel = new Label("Journalisation");
        loggingLabel.setStyle("-fx-font-weight: bold;");
        
        // Section sauvegarde
        Label saveLabel = new Label("Sauvegarde/Chargement");
        saveLabel.setStyle("-fx-font-weight: bold;");
        
        HBox saveLoadBox = new HBox(5);
        saveLoadBox.getChildren().addAll(saveButton, loadButton);
        
        HBox refreshBox = new HBox(5);
        refreshBox.getChildren().addAll(savedDrawingsCombo, refreshButton);
        
        // Assemblage du panneau gauche
        leftPanel.getChildren().addAll(
            shapesLabel, shapesBox,
            new Separator(),
            colorLabel, colorPicker,
            new Separator(),
            strokeLabel, strokeWidthSlider, strokeValueLabel,
            new Separator(),
            loggingLabel, loggingStrategyCombo,
            new Separator(),
            saveLabel, drawingNameField, saveLoadBox, refreshBox,
            new Separator(),
            clearButton
        );
        
        return leftPanel;
    }
    
    private VBox createRightPanel() {
        VBox rightPanel = new VBox(10);
        rightPanel.setPadding(new Insets(10));
        rightPanel.setPrefWidth(250);

        Label logLabel = new Label("Journal des actions");
        logLabel.setStyle("-fx-font-weight: bold;");

        rightPanel.getChildren().addAll(logLabel, logArea);

        return rightPanel;
    }

    private void setupStyling() {
        // Style pour la zone de dessin
        canvas.setStyle("-fx-border-color: #cccccc; -fx-border-width: 1px;");

        // Style pour les boutons
        String buttonStyle = "-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold;";
        saveButton.setStyle(buttonStyle);
        loadButton.setStyle(buttonStyle);
        refreshButton.setStyle(buttonStyle);

        clearButton.setStyle("-fx-background-color: #f44336; -fx-text-fill: white; -fx-font-weight: bold;");

        // Style pour la zone de log
        logArea.setStyle("-fx-font-family: 'Courier New'; -fx-font-size: 11px;");
    }

    // Getters pour le contrôleur
    public BorderPane getRoot() { return root; }
    public Canvas getCanvas() { return canvas; }
    public GraphicsContext getGraphicsContext() { return gc; }

    public ToggleGroup getShapeToggleGroup() { return shapeToggleGroup; }
    public RadioButton getRectangleRadio() { return rectangleRadio; }
    public RadioButton getCircleRadio() { return circleRadio; }
    public RadioButton getLineRadio() { return lineRadio; }

    public ColorPicker getColorPicker() { return colorPicker; }
    public Slider getStrokeWidthSlider() { return strokeWidthSlider; }
    public ComboBox<String> getLoggingStrategyCombo() { return loggingStrategyCombo; }

    public TextField getDrawingNameField() { return drawingNameField; }
    public ComboBox<String> getSavedDrawingsCombo() { return savedDrawingsCombo; }
    public TextArea getLogArea() { return logArea; }

    public Button getClearButton() { return clearButton; }
    public Button getSaveButton() { return saveButton; }
    public Button getLoadButton() { return loadButton; }
    public Button getRefreshButton() { return refreshButton; }

    /**
     * Retourne le type de forme sélectionné
     * @return Type de forme sélectionné
     */
    public ShapeFactory.ShapeType getSelectedShapeType() {
        RadioButton selected = (RadioButton) shapeToggleGroup.getSelectedToggle();
        if (selected == rectangleRadio) {
            return ShapeFactory.ShapeType.RECTANGLE;
        } else if (selected == circleRadio) {
            return ShapeFactory.ShapeType.CIRCLE;
        } else if (selected == lineRadio) {
            return ShapeFactory.ShapeType.LINE;
        }
        return ShapeFactory.ShapeType.RECTANGLE; // Par défaut
    }

    /**
     * Ajoute un message au journal des actions
     * @param message Message à ajouter
     */
    public void addLogMessage(String message) {
        logArea.appendText(message + "\n");
        logArea.setScrollTop(Double.MAX_VALUE); // Scroll vers le bas
    }

    /**
     * Efface la zone de dessin
     */
    public void clearCanvas() {
        gc.clearRect(0, 0, canvas.getWidth(), canvas.getHeight());
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
    }
}
