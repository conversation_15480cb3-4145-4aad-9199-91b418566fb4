# Guide d'Installation pour Eclipse

## Étapes détaillées pour configurer le projet dans Eclipse

### 1. Prérequis
- Eclipse IDE (version récente recommandée)
- Java 11 ou supérieur
- JavaFX SDK 17 ou supérieur

### 2. Téléchargement de JavaFX

1. **Aller sur** [https://openjfx.io/](https://openjfx.io/)
2. **Télécharger** JavaFX SDK pour votre OS
3. **Extraire** dans un dossier permanent (ex: `C:\javafx-sdk-17.0.2`)

### 3. Configuration d'Eclipse

#### A. C<PERSON>er une User Library pour JavaFX

1. **Ouvrir Eclipse**
2. **Aller dans** `Window > Preferences`
3. **Naviguer vers** `Java > Build Path > User Libraries`
4. **Cliquer** sur `New...`
5. **Nom** : `JavaFX`
6. **C<PERSON>r** sur `OK`
7. **Sélectionner** la library `JavaFX` créée
8. **Cliquer** sur `Add External JARs...`
9. **Naviguer** vers le dossier `javafx-sdk-17.0.2/lib`
10. **Sélectionner** tous les fichiers `.jar` :
    - `javafx.base.jar`
    - `javafx.controls.jar`
    - `javafx.fxml.jar`
    - `javafx.graphics.jar`
    - `javafx.media.jar`
    - `javafx.swing.jar`
    - `javafx.web.jar`
11. **Cliquer** sur `OK` puis `Apply and Close`

#### B. Importer le projet

1. **File > Import**
2. **General > Existing Projects into Workspace**
3. **Browse** vers le dossier du projet
4. **Sélectionner** le projet et cliquer `Finish`

### 4. Configuration du lancement

#### A. Créer une configuration de lancement

1. **Clic droit** sur `Main.java`
2. **Run As > Run Configurations...**
3. **Java Application > New Configuration**
4. **Name** : `DrawingApp`
5. **Project** : `DrawingApp`
6. **Main class** : `com.drawingapp.Main`

#### B. Configurer les arguments VM

Dans l'onglet **Arguments**, section **VM arguments** :

```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml
```

**Remplacer** le chemin par votre installation JavaFX.

#### C. Appliquer et lancer

1. **Cliquer** sur `Apply`
2. **Cliquer** sur `Run`

### 5. Vérification

Si tout est configuré correctement :
- L'application se lance sans erreur
- Une fenêtre JavaFX s'ouvre avec l'interface de dessin
- Vous pouvez dessiner des formes

### 6. Dépannage

#### Erreur "Module javafx.controls not found"
- Vérifier que JavaFX SDK est téléchargé
- Vérifier le chemin dans les arguments VM
- Vérifier que la User Library JavaFX est configurée

#### Erreur "SQLite JDBC not found"
- Exécuter `download_dependencies.bat` (Windows) ou `download_dependencies.sh` (Linux/Mac)
- Vérifier que `sqlite-jdbc-********.jar` est dans le dossier `lib/`

#### Erreur de compilation
- Vérifier que le projet utilise Java 11+
- Refresh le projet (F5)
- Clean et rebuild (Project > Clean)

### 7. Test rapide

Pour tester sans JavaFX :
1. **Clic droit** sur `TestApp.java`
2. **Run As > Java Application**
3. Vérifier la sortie console

### 8. Structure finale

```
DrawingApp/
├── src/com/drawingapp/
├── lib/sqlite-jdbc-********.jar
├── bin/ (généré par Eclipse)
├── .project
├── .classpath
└── README.md
```

## Félicitations !

Votre projet est maintenant configuré et prêt à être utilisé dans Eclipse.
