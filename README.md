# Application de Dessin JavaFX avec Design Patterns

## Description

Cette application JavaFX permet de dessiner des formes géométriques (rectangle, cercle, ligne) sur une zone de dessin. L'architecture s'appuie sur plusieurs design patterns pour assurer une meilleure modularité, extensibilité et maintenance du code.

**Projet Java Standard pour Eclipse (non-Maven)**

## Fonctionnalités

- ✅ **Sélection de formes** : Rectangle, Cercle, Ligne
- ✅ **Dessin interactif** : Cliquer-glisser pour dessiner
- ✅ **Personnalisation** : Couleur et épaisseur du trait
- ✅ **Sauvegarde/Chargement** : Persistance en base de données SQLite
- ✅ **Journalisation** : 3 stratégies (Console, Fichier, Base de données)
- ✅ **Interface intuitive** : Palette d'outils et zone de log

## Design Patterns Utilisés

### 1. Factory Pattern
- **Classe** : `ShapeFactory`
- **Usage** : Création des différentes formes géométriques
- **Avantage** : Centralise la création d'objets et facilite l'ajout de nouvelles formes

### 2. Strategy Pattern
- **Interface** : `LoggingStrategy`
- **Implémentations** : `ConsoleLoggingStrategy`, `FileLoggingStrategy`, `DatabaseLoggingStrategy`
- **Usage** : Différentes stratégies de journalisation
- **Avantage** : Changement dynamique de stratégie sans modification du code client

### 3. Observer Pattern
- **Interface** : `DrawingObserver`
- **Usage** : Notification des changements dans le dessin
- **Avantage** : Découplage entre le modèle et les observateurs

### 4. Singleton Pattern
- **Classe** : `DatabaseManager`
- **Usage** : Gestion unique de la connexion à la base de données
- **Avantage** : Une seule instance de connexion partagée

### 5. Template Method Pattern
- **Classe** : `Shape` (classe abstraite)
- **Usage** : Structure commune pour le rendu des formes
- **Avantage** : Code réutilisable avec personnalisation par sous-classe

### 6. MVC Pattern
- **Model** : Classes dans `com.drawingapp.model`
- **View** : `DrawingView`
- **Controller** : `DrawingController`
- **Avantage** : Séparation claire des responsabilités

## Structure du Projet

```
DrawingApp/
├── src/
│   └── com/
│       └── drawingapp/
│           ├── Main.java                    # Point d'entrée
│           ├── controller/
│           │   └── DrawingController.java   # Contrôleur MVC
│           ├── model/
│           │   ├── shapes/                  # Formes géométriques
│           │   │   ├── Shape.java           # Classe abstraite
│           │   │   ├── Rectangle.java
│           │   │   ├── Circle.java
│           │   │   └── Line.java
│           │   ├── drawing/                 # Gestion des dessins
│           │   │   ├── Drawing.java
│           │   │   └── DrawingService.java
│           │   └── database/                # Base de données
│           │       └── DatabaseManager.java
│           ├── view/
│           │   └── DrawingView.java         # Interface utilisateur
│           └── patterns/                    # Design patterns
│               ├── factory/
│               │   └── ShapeFactory.java
│               ├── strategy/
│               │   ├── LoggingStrategy.java
│               │   ├── ConsoleLoggingStrategy.java
│               │   ├── FileLoggingStrategy.java
│               │   └── DatabaseLoggingStrategy.java
│               └── observer/
│                   └── DrawingObserver.java
├── lib/
│   └── sqlite-jdbc-********.jar           # Dépendance SQLite
├── bin/                                     # Classes compilées
├── .project                                 # Configuration Eclipse
├── .classpath                               # Classpath Eclipse
└── README.md
```

## Prérequis

- **Java 11** ou supérieur
- **Eclipse IDE** (ou tout autre IDE Java)
- **JavaFX SDK 17** ou supérieur
- **SQLite JDBC** (inclus dans le projet)

## Installation et Configuration

### 1. Télécharger les dépendances
```bash
# Sur Windows
download_dependencies.bat

# Sur Linux/Mac
./download_dependencies.sh
```

### 2. Configurer JavaFX dans Eclipse

1. **Télécharger JavaFX SDK** depuis [https://openjfx.io/](https://openjfx.io/)
2. **Extraire** le SDK dans un dossier (ex: `C:\javafx-sdk-17.0.2`)
3. **Dans Eclipse** :
   - Aller dans `Window > Preferences > Java > Build Path > User Libraries`
   - Cliquer sur `New...` et créer une library appelée `JavaFX`
   - Sélectionner la library `JavaFX` et cliquer sur `Add External JARs...`
   - Ajouter tous les JAR du dossier `javafx-sdk-17.0.2/lib`

### 3. Importer le projet dans Eclipse

1. **File > Import > General > Existing Projects into Workspace**
2. **Sélectionner** le dossier du projet
3. **Cliquer** sur Finish

### 4. Configurer les arguments de lancement

1. **Clic droit** sur `Main.java > Run As > Run Configurations`
2. **Dans l'onglet Arguments**, ajouter dans VM arguments :
```
--module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml
```
(Remplacer le chemin par votre installation JavaFX)

### 5. Exécuter l'application

- **Clic droit** sur `Main.java > Run As > Java Application`

## Utilisation

### Interface Utilisateur

1. **Panneau gauche** : Outils de dessin
   - Sélection de forme (Rectangle, Cercle, Ligne)
   - Choix de couleur
   - Réglage de l'épaisseur du trait
   - Stratégie de journalisation
   - Sauvegarde/Chargement

2. **Panneau central** : Zone de dessin
   - Cliquer-glisser pour dessiner une forme
   - Prévisualisation en temps réel

3. **Panneau droit** : Journal des actions
   - Affichage des logs en temps réel

### Fonctionnalités Avancées

- **Sauvegarde** : Saisir un nom et cliquer "Sauvegarder"
- **Chargement** : Sélectionner un dessin et cliquer "Charger"
- **Journalisation** : Changer la stratégie dans la liste déroulante
- **Effacement** : Bouton "Effacer" pour vider la zone de dessin

## Base de Données

L'application utilise SQLite avec les tables suivantes :

- **drawings** : Stockage des métadonnées des dessins
- **shapes** : Stockage des formes géométriques
- **logs** : Journalisation en base de données (si activée)

## Compilation manuelle (optionnelle)

Si vous préférez compiler en ligne de commande :

```bash
# Compiler
javac -cp "lib/*" -d bin src/com/drawingapp/**/*.java

# Exécuter
java -cp "bin;lib/*" --module-path "C:\javafx-sdk-17.0.2\lib" --add-modules javafx.controls,javafx.fxml com.drawingapp.Main
```

## Extension Future

L'architecture modulaire permet facilement d'ajouter :
- Nouvelles formes géométriques
- Nouvelles stratégies de journalisation
- Fonctionnalités d'export (PNG, SVG, etc.)
- Algorithmes de graphes (plus court chemin)
- Undo/Redo avec Command Pattern

## Auteur

Développé avec les design patterns pour une architecture robuste et extensible.
