@echo off
echo Lancement de l'application de dessin...

REM Vérifier si les classes sont compilées
if not exist bin\com\drawingapp\Main.class (
    echo Compilation des sources...
    javac -cp "lib/*" -d bin src/com/drawingapp/**/*.java
    if errorlevel 1 (
        echo Erreur de compilation !
        pause
        exit /b 1
    )
)

REM Définir le chemin JavaFX (à modifier selon votre installation)
set JAVAFX_PATH=C:\javafx-sdk-17.0.2\lib

REM Vérifier si JavaFX est disponible
if not exist "%JAVAFX_PATH%" (
    echo ERREUR: JavaFX SDK non trouvé dans %JAVAFX_PATH%
    echo Veuillez :
    echo 1. Télécharger JavaFX SDK depuis https://openjfx.io/
    echo 2. Modifier la variable JAVAFX_PATH dans ce script
    pause
    exit /b 1
)

echo Démarrage de l'application...
java -cp "bin;lib/*" --module-path "%JAVAFX_PATH%" --add-modules javafx.controls,javafx.fxml,javafx.graphics,javafx.base com.drawingapp.Main

if errorlevel 1 (
    echo.
    echo ERREUR DE LANCEMENT !
    echo Vérifiez que :
    echo 1. JavaFX SDK est installé dans %JAVAFX_PATH%
    echo 2. Le projet est compilé (dossier bin existe)
    echo 3. Java 11+ est utilisé
    echo.
)

pause
