package com.drawingapp.model.shapes;

import javafx.scene.canvas.GraphicsContext;

/**
 * Implémentation concrète d'un rectangle
 */
public class Rectangle extends Shape {
    
    public Rectangle(double startX, double startY, double endX, double endY) {
        super(startX, startY, endX, endY);
        this.shapeType = "Rectangle";
    }
    
    @Override
    protected void drawShape(GraphicsContext gc) {
        double x = Math.min(startX, endX);
        double y = Math.min(startY, endY);
        double width = Math.abs(endX - startX);
        double height = Math.abs(endY - startY);
        
        gc.strokeRect(x, y, width, height);
    }
}
